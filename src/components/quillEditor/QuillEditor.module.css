/* QuillEditor 容器样式 */
.quillContainer {
  position: relative;
  transition: all 0.3s ease;
}

/* 全屏模式样式 */
.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: white;
  padding: 20px;
  box-sizing: border-box;
}

.fullscreen :global(.ql-container) {
  height: calc(100vh - 120px) !important;
  font-size: 16px;
}

.fullscreen :global(.ql-editor) {
  height: 100% !important;
  overflow-y: auto;
}

/* 全屏按钮样式 */
:global(.ql-toolbar .ql-fullscreen) {
  width: 28px;
  height: 28px;
  padding: 4px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

:global(.ql-toolbar .ql-fullscreen:hover) {
  background-color: #f0f0f0;
}

:global(.ql-toolbar .ql-fullscreen:active) {
  background-color: #e0e0e0;
}

/* 全屏图标样式 */
:global(.fullscreen-icon) {
  width: 16px;
  height: 16px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

:global(.fullscreen-icon.expand) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='m21 21-6-6m6 6v-4.8m0 4.8h-4.8'/%3E%3Cpath d='M3 16.2V21m0 0h4.8M3 21l6-6'/%3E%3Cpath d='M21 7.8V3m0 0h-4.8M21 3l-6 6'/%3E%3Cpath d='M3 7.8V3m0 0h4.8M3 3l6 6'/%3E%3C/svg%3E");
}

:global(.fullscreen-icon.minimize) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 3v3a2 2 0 0 1-2 2H3'/%3E%3Cpath d='m3 3 6 6'/%3E%3Cpath d='M8 21v-3a2 2 0 0 1 2-2h3'/%3E%3Cpath d='m21 21-6-6'/%3E%3Cpath d='M16 3h3a2 2 0 0 1 2 2v3'/%3E%3Cpath d='m21 3-6 6'/%3E%3Cpath d='M16 21v-3a2 2 0 0 1-2-2H3'/%3E%3Cpath d='m3 21 6-6'/%3E%3C/svg%3E");
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fullscreen {
    padding: 10px;
  }

  .fullscreen :global(.ql-container) {
    height: calc(100vh - 100px) !important;
  }
}
